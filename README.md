# WatchIndex

A modern movie and TV show discovery application built with React, TypeScript, and the TMDB API.

## Features

- 🎬 Browse popular, top-rated, and upcoming movies
- 📺 Discover popular and trending TV shows
- 🔍 Search for movies and TV shows
- 📱 Responsive design with Tailwind CSS
- 🎨 Modern UI components with shadcn/ui
- ⚡ Fast development with Vite

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui
- **API**: The Movie Database (TMDB) API
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router DOM

## Getting Started

### Prerequisites

- Node.js 18+ or Bun
- TMDB API key (free registration at [themoviedb.org](https://www.themoviedb.org/))

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd vistio
```

2. Install dependencies:
```bash
npm install
# or
bun install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Add your TMDB API credentials to `.env`:
```env
VITE_TMDB_API_KEY=your_api_key_here
VITE_TMDB_READ_ACCESS_TOKEN=your_read_access_token_here
```

5. Start the development server:
```bash
npm run dev
# or
bun dev
```

6. Open [http://localhost:8080](http://localhost:8080) in your browser.

## Getting TMDB API Keys

1. Create a free account at [The Movie Database](https://www.themoviedb.org/)
2. Go to your account settings → API
3. Request an API key (choose "Developer" option)
4. Copy both the API Key and Read Access Token to your `.env` file

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── hero/           # Hero section components
│   ├── layout/         # Layout components (Header, Footer)
│   ├── movie/          # Movie-related components
│   └── ui/             # shadcn/ui components
├── hooks/              # Custom React hooks
├── pages/              # Page components
├── services/           # API services
├── types/              # TypeScript type definitions
└── lib/                # Utility functions
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## API Integration

The application uses the TMDB API v3 for all movie and TV show data. The API service is located in `src/services/tmdbApi.ts` and provides methods for:

- Fetching popular, top-rated, and upcoming movies
- Getting popular and trending TV shows
- Searching movies and TV shows
- Retrieving detailed information for specific titles

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.