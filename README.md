<div align="center">
  <img src="https://img.shields.io/badge/React-18.3.1-61DAFB?style=for-the-badge&logo=react&logoColor=white" alt="React" />
  <img src="https://img.shields.io/badge/TypeScript-5.5.3-3178C6?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Vite-5.4.1-646CFF?style=for-the-badge&logo=vite&logoColor=white" alt="Vite" />
  <img src="https://img.shields.io/badge/Tailwind_CSS-3.4.11-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white" alt="Tailwind CSS" />
</div>

# 🎬 WatchIndex

> **Your Ultimate Movie & TV Database** - A modern, responsive web application for discovering movies, TV shows, cast information, and more.

**🌐 Live Demo:** [https://watchindex.vercel.app/](https://watchindex.vercel.app/)

**📂 Repository:** [https://github.com/cjjutba/watchindex](https://github.com/cjjutba/watchindex)

---

## 📋 Table of Contents

- [✨ Features](#-features)
- [🛠️ Tech Stack](#️-tech-stack)
- [🎨 Design System](#-design-system)
- [🚀 Getting Started](#-getting-started)
- [📁 Project Structure](#-project-structure)
- [🔧 Available Scripts](#-available-scripts)
- [🌐 API Integration](#-api-integration)
- [📱 Responsive Design](#-responsive-design)
- [⚡ Performance](#-performance)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [👨‍💻 Author](#-author)

---

## ✨ Features

### 🎭 Core Functionality
- **🎬 Movie Discovery**: Browse popular, top-rated, upcoming, and now-playing movies
- **📺 TV Show Exploration**: Discover popular and trending TV shows with detailed season/episode information
- **🔍 Advanced Search**: Multi-type search with real-time suggestions and filtering
- **👤 Person Profiles**: Detailed cast and crew information with filmographies
- **🎯 Genre Browsing**: Explore content by genres with advanced filtering
- **⭐ Favorites System**: Save and manage your favorite movies and TV shows (localStorage)
- **🎪 Now Playing**: Current theatrical releases with location-based information

### 🎨 User Experience
- **📱 Fully Responsive**: Optimized for desktop, tablet, and mobile devices
- **🌙 Cinema-Inspired Design**: Dark theme with gold accents and premium aesthetics
- **⚡ Fast Performance**: Optimized with React Query caching and lazy loading
- **🎯 Intuitive Navigation**: Clean, professional interface with smooth transitions
- **🔄 Real-time Updates**: Live search suggestions and dynamic content loading
- **📊 Rich Media**: High-quality images, trailers, and detailed metadata

### 🔧 Technical Features
- **🎪 Watch Providers**: Integration with streaming platforms and rental services
- **🔗 External Links**: Direct links to IMDb, social media, and official websites
- **📈 Analytics Ready**: Structured for SEO and performance monitoring
- **🛡️ Type Safety**: Full TypeScript implementation with strict typing
- **♿ Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation

---

## 🛠️ Tech Stack

### Frontend
- **⚛️ React 18.3.1** - Modern React with hooks and concurrent features
- **📘 TypeScript 5.5.3** - Full type safety and enhanced developer experience
- **⚡ Vite 5.4.1** - Lightning-fast build tool and development server
- **🎨 Tailwind CSS 3.4.11** - Utility-first CSS framework
- **🧩 shadcn/ui** - High-quality, accessible component library

### State Management & Data Fetching
- **🔄 TanStack Query 5.56.2** - Powerful data synchronization for React
- **🌐 Axios 1.10.0** - Promise-based HTTP client
- **📍 React Router DOM 6.26.2** - Declarative routing for React

### UI Components & Styling
- **🎭 Radix UI** - Unstyled, accessible components
- **🎨 Lucide React** - Beautiful & consistent icon library
- **🎪 Embla Carousel** - Lightweight carousel library
- **🎯 Class Variance Authority** - Component variant management

### Development Tools
- **📏 ESLint** - Code linting and formatting
- **🔧 PostCSS & Autoprefixer** - CSS processing and vendor prefixes
- **📦 Component Tagger** - Development component identification

---

## 🎨 Design System

WatchIndex features a **cinema-inspired dark theme** with a carefully crafted design system:

### 🎨 Color Palette
- **Primary Gold**: `#F1C40F` - Used for ratings, highlights, and CTAs
- **Cinema Black**: `#0A0A0B` - Primary background
- **Cinema Gray**: `#1A1A1B` - Secondary backgrounds and cards
- **Cinema White**: `#F8FAFC` - Primary text color

### 🎭 Visual Elements
- **Premium Gradients**: Subtle gradients for depth and visual interest
- **Cinema Shadows**: Carefully crafted shadows for card elevation
- **Smooth Transitions**: 300ms cubic-bezier transitions throughout
- **Gold Glow Effects**: Subtle glow effects for interactive elements

### 📱 Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px - 1400px
- **Large Desktop**: > 1400px

---

## 🚀 Getting Started

### 📋 Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js 18+** or **Bun** (latest version)
- **TMDB API Key** (free registration at [themoviedb.org](https://www.themoviedb.org/))

### 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/cjjutba/watchindex.git
   cd watchindex
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or using bun
   bun install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

4. **Configure TMDB API credentials**

   Add your TMDB API credentials to `.env`:
   ```env
   VITE_TMDB_API_KEY=your_api_key_here
   VITE_TMDB_READ_ACCESS_TOKEN=your_read_access_token_here
   VITE_TMDB_BASE_URL=https://api.themoviedb.org/3
   VITE_TMDB_IMAGE_BASE_URL=https://image.tmdb.org/t/p/
   ```

5. **Start the development server**
   ```bash
   npm run dev
   # or using bun
   bun dev
   ```

6. **Open your browser**

   Navigate to [http://localhost:8080](http://localhost:8080) to see the application.

### 🔑 Getting TMDB API Keys

1. **Create Account**: Sign up for a free account at [The Movie Database](https://www.themoviedb.org/)
2. **Navigate to API Settings**: Go to your account settings → API
3. **Request API Key**: Choose "Developer" option and fill out the application
4. **Copy Credentials**: Copy both the API Key and Read Access Token to your `.env` file

---

## 📁 Project Structure

```
watchindex/
├── 📁 public/                    # Static assets
├── 📁 src/
│   ├── 📁 components/            # Reusable UI components
│   │   ├── 📁 common/           # Shared components (ScrollToTop, etc.)
│   │   ├── 📁 episode/          # Episode-specific components
│   │   ├── 📁 externalLinks/    # External link components
│   │   ├── 📁 genre/            # Genre browsing components
│   │   ├── 📁 hero/             # Hero section components
│   │   ├── 📁 layout/           # Layout components (Header, Footer)
│   │   ├── 📁 movie/            # Movie-related components
│   │   ├── 📁 nowPlaying/       # Now playing section components
│   │   ├── 📁 person/           # Person/cast components
│   │   ├── 📁 search/           # Search functionality components
│   │   ├── 📁 season/           # TV season components
│   │   ├── 📁 ui/               # shadcn/ui base components
│   │   └── 📁 watchProvider/    # Streaming provider components
│   ├── 📁 contexts/             # React contexts
│   │   └── FavoritesContext.tsx # Favorites management
│   ├── 📁 hooks/                # Custom React hooks
│   │   ├── useAdvancedSearch.ts # Advanced search functionality
│   │   ├── useExternalLinks.ts  # External links integration
│   │   ├── useGenres.ts         # Genre data management
│   │   ├── useMovies.ts         # Movie data fetching
│   │   ├── useNowPlaying.ts     # Now playing data
│   │   ├── usePeople.ts         # Person data management
│   │   ├── useSearchWithDropdown.ts # Search with suggestions
│   │   ├── useSeasons.ts        # TV season data
│   │   ├── useTVShows.ts        # TV show data fetching
│   │   └── useWatchProviders.ts # Streaming providers
│   ├── 📁 lib/                  # Utility functions
│   │   └── utils.ts             # Common utilities
│   ├── 📁 pages/                # Page components
│   │   ├── Contact.tsx          # Contact page
│   │   ├── Discover.tsx         # Advanced discovery page
│   │   ├── EpisodeDetail.tsx    # Individual episode details
│   │   ├── Favorites.tsx        # User favorites page
│   │   ├── GenreDetail.tsx      # Genre-specific browsing
│   │   ├── Genres.tsx           # All genres overview
│   │   ├── Home.tsx             # Homepage
│   │   ├── Index.tsx            # Main app wrapper
│   │   ├── MovieDetail.tsx      # Individual movie details
│   │   ├── Movies.tsx           # Movies browsing page
│   │   ├── NotFound.tsx         # 404 error page
│   │   ├── NowPlaying.tsx       # Now playing movies
│   │   ├── PersonDetail.tsx     # Cast/crew details
│   │   ├── PrivacyPolicy.tsx    # Privacy policy
│   │   ├── SearchResults.tsx    # Search results page
│   │   ├── SeasonDetail.tsx     # TV season details
│   │   ├── TermsOfService.tsx   # Terms of service
│   │   ├── TVShowDetail.tsx     # Individual TV show details
│   │   └── TVShows.tsx          # TV shows browsing page
│   ├── 📁 services/             # API services
│   │   └── tmdbApi.ts           # TMDB API integration
│   ├── 📁 types/                # TypeScript type definitions
│   │   ├── api.ts               # API response types
│   │   ├── externalLinks.ts     # External links types
│   │   ├── genre.ts             # Genre-related types
│   │   ├── movie.ts             # Movie data types
│   │   ├── person.ts            # Person data types
│   │   ├── season.ts            # TV season types
│   │   ├── tvshow.ts            # TV show types
│   │   └── watchProvider.ts     # Streaming provider types
│   ├── App.tsx                  # Main app component
│   ├── index.css                # Global styles & design system
│   └── main.tsx                 # App entry point
├── 📄 .env.example              # Environment variables template
├── 📄 components.json           # shadcn/ui configuration
├── 📄 eslint.config.js          # ESLint configuration
├── 📄 index.html                # HTML template
├── 📄 package.json              # Dependencies and scripts
├── 📄 tailwind.config.ts        # Tailwind CSS configuration
├── 📄 tsconfig.json             # TypeScript configuration
└── 📄 vite.config.ts            # Vite configuration
```

---

## 🔧 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | 🚀 Start development server on port 8080 |
| `npm run build` | 🏗️ Build for production |
| `npm run build:dev` | 🔧 Build in development mode |
| `npm run preview` | 👀 Preview production build locally |
| `npm run lint` | 🔍 Run ESLint for code quality |

---

## 🌐 API Integration

WatchIndex integrates with **The Movie Database (TMDB) API v3** to provide comprehensive entertainment data:

### 🎬 Movie Endpoints
- **Popular Movies**: Trending and popular movie listings
- **Top Rated**: Highest-rated movies of all time
- **Upcoming**: Soon-to-be-released movies
- **Now Playing**: Currently in theaters
- **Movie Details**: Complete movie information with cast, crew, videos, and recommendations
- **Movie Search**: Advanced search with filters

### 📺 TV Show Endpoints
- **Popular TV Shows**: Trending television series
- **Top Rated TV**: Highest-rated TV shows
- **TV Show Details**: Complete series information including seasons and episodes
- **Season Details**: Individual season information with episode lists
- **Episode Details**: Specific episode information and metadata

### 👤 Person Endpoints
- **Person Details**: Actor/director profiles with biographies
- **Person Credits**: Complete filmography (movies and TV)
- **Person Images**: Profile photos and headshots
- **External IDs**: Links to IMDb, social media, and official websites

### 🎭 Additional Features
- **Genre Discovery**: Browse content by genre with advanced filtering
- **Watch Providers**: Streaming availability by region
- **External Links**: Integration with IMDb, social platforms, and official sites
- **Multi-Search**: Unified search across movies, TV shows, and people
- **Image Optimization**: Multiple image sizes for optimal performance

### 🔧 API Service Architecture

The API service (`src/services/tmdbApi.ts`) provides:
- **Type-Safe Requests**: Full TypeScript integration
- **Error Handling**: Comprehensive error management
- **Response Caching**: Optimized with TanStack Query
- **Image URL Generation**: Dynamic image sizing
- **Rate Limiting**: Respectful API usage

---

## 📱 Responsive Design

WatchIndex is built with a **mobile-first approach** ensuring optimal experience across all devices:

### 🖥️ Desktop (≥1024px)
- **Multi-column layouts** with optimal content density
- **Hover effects** and interactive elements
- **Large hero sections** with full viewport utilization
- **Advanced filtering** and sorting options

### 📱 Tablet (768px - 1023px)
- **Adaptive grid systems** that scale gracefully
- **Touch-optimized** interface elements
- **Collapsible navigation** for better space utilization
- **Optimized image sizes** for faster loading

### 📱 Mobile (<768px)
- **Single-column layouts** for easy scrolling
- **Touch-friendly** buttons and interactive elements
- **Hamburger menu** navigation
- **Swipe gestures** for carousels and galleries
- **Optimized typography** for readability

### 🎨 Design Consistency
- **Consistent spacing** using Tailwind's spacing scale
- **Responsive typography** that scales appropriately
- **Flexible components** that adapt to container sizes
- **Optimized images** with proper aspect ratios

---

## ⚡ Performance

WatchIndex is optimized for speed and efficiency:

### 🚀 Core Optimizations
- **Vite Build System**: Lightning-fast development and optimized production builds
- **React 18 Features**: Concurrent rendering and automatic batching
- **Code Splitting**: Automatic route-based code splitting
- **Tree Shaking**: Eliminates unused code from bundles

### 🔄 Data Management
- **TanStack Query**: Intelligent caching, background updates, and stale-while-revalidate
- **Request Deduplication**: Prevents duplicate API calls
- **Optimistic Updates**: Immediate UI feedback for better UX
- **Background Refetching**: Keeps data fresh without user intervention

### 🖼️ Image Optimization
- **Progressive Loading**: Images load progressively with skeleton states
- **Multiple Sizes**: TMDB's multiple image sizes for optimal bandwidth usage
- **Lazy Loading**: Images load only when needed
- **Error Fallbacks**: Graceful handling of missing images

### 📊 Bundle Analysis
- **Optimized Dependencies**: Carefully selected lightweight libraries
- **CSS Purging**: Unused CSS automatically removed in production
- **Asset Optimization**: Images and fonts optimized for web delivery

---

## 🤝 Contributing

We welcome contributions to WatchIndex! Here's how you can help:

### 🐛 Bug Reports
1. **Check existing issues** to avoid duplicates
2. **Use the issue template** with detailed information
3. **Include screenshots** and steps to reproduce
4. **Specify your environment** (browser, OS, etc.)

### ✨ Feature Requests
1. **Search existing requests** to avoid duplicates
2. **Describe the feature** and its benefits
3. **Provide mockups** or examples if possible
4. **Consider implementation complexity**

### 🔧 Development Workflow
1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow the coding standards**: Use ESLint and Prettier
4. **Write meaningful commits**: Follow conventional commit format
5. **Test your changes**: Ensure everything works as expected
6. **Submit a pull request**: Include detailed description of changes

### 📝 Code Standards
- **TypeScript**: Use strict typing throughout
- **Component Structure**: Follow established patterns
- **Styling**: Use Tailwind CSS classes consistently
- **Accessibility**: Ensure WCAG compliance
- **Performance**: Consider bundle size and runtime performance

---

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### 📋 License Summary
- ✅ **Commercial use** allowed
- ✅ **Modification** allowed
- ✅ **Distribution** allowed
- ✅ **Private use** allowed
- ❌ **Liability** not provided
- ❌ **Warranty** not provided

---

## 👨‍💻 Author

**Christian Jutba**
- 🌐 **Portfolio**: [https://cjjutba.site/]
- 💼 **LinkedIn**: [https://www.linkedin.com/in/cjjutba/](https://www.linkedin.com/in/cjjutba/)
- 🐙 **GitHub**: [https://github.com/cjjutba](https://github.com/cjjutba)
- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)

### 🎯 About This Project
WatchIndex was created as a **portfolio project** to demonstrate modern React development skills, including:
- **Advanced TypeScript** usage and type safety
- **Modern React patterns** with hooks and context
- **Professional UI/UX design** with accessibility in mind
- **API integration** and data management
- **Responsive design** and mobile optimization
- **Performance optimization** and best practices

---

<div align="center">
  <h3>🌟 If you found this project helpful, please consider giving it a star! 🌟</h3>

  [![GitHub stars](https://img.shields.io/github/stars/cjjutba/watchindex?style=social)](https://github.com/cjjutba/watchindex/stargazers)
  [![GitHub forks](https://img.shields.io/github/forks/cjjutba/watchindex?style=social)](https://github.com/cjjutba/watchindex/network/members)

  **Built with ❤️ by CJ Jutba**
</div>